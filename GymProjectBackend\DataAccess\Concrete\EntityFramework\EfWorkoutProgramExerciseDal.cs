using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramExerciseDal : EfCompanyEntityRepositoryBase<WorkoutProgramExercise, GymContext>, IWorkoutProgramExerciseDal
    {
        // Enterprise multi-tenant constructor injection pattern (100.000+ users ready)
        public EfWorkoutProgramExerciseDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
        {
        }
    }
}
