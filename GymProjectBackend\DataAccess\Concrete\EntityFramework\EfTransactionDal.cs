﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfTransactionDal : EfCompanyEntityRepositoryBase<Transaction, GymContext>, ITransactionDal
    {
        // Enterprise multi-tenant constructor injection pattern (100.000+ users ready)
        public EfTransactionDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
        {
        }

        // Enterprise multi-tenant method (memory leak fixed)
        public List<TransactionDetailDto> GetTransactionsWithDetails()
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var result = from t in _context.Transactions
                         join m in _context.Members on t.MemberID equals m.MemberID
                         join p in _context.Products on t.ProductID equals p.ProductID into productJoin
                         from p in productJoin.DefaultIfEmpty()
                         where t.CompanyID == companyId // Enterprise multi-tenant filtering
                         && m.CompanyID == companyId // Enterprise multi-tenant security
                         && (p == null || p.CompanyID == companyId) // Enterprise multi-tenant security
                         && t.IsActive == true // Sadece aktif (silinmemiş) işlemler
                         orderby t.TransactionDate descending // Enterprise performance optimization
                         select new TransactionDetailDto
                         {
                             TransactionID = t.TransactionID,
                             MemberID = t.MemberID,
                             MemberName = m.Name,
                             ProductID = t.ProductID,
                             ProductName = p != null ? p.Name : null,
                             Amount = t.Amount,
                             UnitPrice = t.UnitPrice,
                             TransactionType = t.TransactionType,
                             TransactionDate = t.TransactionDate,
                             Quantity = t.Quantity,
                             IsPaid = t.IsPaid,
                             Balance = m.Balance,
                             TotalPrice = t.TransactionType == "Satış" ? t.UnitPrice * t.Quantity : t.Amount
                         };

            return result.ToList();
        }

        // Enterprise async multi-tenant method (100.000+ users ready)
        public async Task<List<TransactionDetailDto>> GetTransactionsWithDetailsAsync()
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var result = from t in _context.Transactions
                         join m in _context.Members on t.MemberID equals m.MemberID
                         join p in _context.Products on t.ProductID equals p.ProductID into productJoin
                         from p in productJoin.DefaultIfEmpty()
                         where t.CompanyID == companyId // Enterprise multi-tenant filtering
                         && m.CompanyID == companyId // Enterprise multi-tenant security
                         && (p == null || p.CompanyID == companyId) // Enterprise multi-tenant security
                         && t.IsActive == true // Sadece aktif (silinmemiş) işlemler
                         orderby t.TransactionDate descending // Enterprise performance optimization
                         select new TransactionDetailDto
                         {
                             TransactionID = t.TransactionID,
                             MemberID = t.MemberID,
                             MemberName = m.Name,
                             ProductID = t.ProductID,
                             ProductName = p != null ? p.Name : null,
                             Amount = t.Amount,
                             UnitPrice = t.UnitPrice,
                             TransactionType = t.TransactionType,
                             TransactionDate = t.TransactionDate,
                             Quantity = t.Quantity,
                             IsPaid = t.IsPaid,
                             Balance = m.Balance,
                             TotalPrice = t.TransactionType == "Satış" ? t.UnitPrice * t.Quantity : t.Amount
                         };

            return await result.ToListAsync();
        }
    }
}
