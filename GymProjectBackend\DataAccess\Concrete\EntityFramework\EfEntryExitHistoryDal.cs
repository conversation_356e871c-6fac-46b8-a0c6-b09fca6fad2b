﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfEntryExitHistoryDal : EfCompanyEntityRepositoryBase<EntryExitHistory, GymContext>, IEntryExitHistoryDal
    {
        // Enterprise multi-tenant constructor injection pattern (100.000+ users ready)
        public EfEntryExitHistoryDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
        {
        }
    }
}
