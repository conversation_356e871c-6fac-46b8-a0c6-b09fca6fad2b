﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Paging;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfProductDal : EfCompanyEntityRepositoryBase<Product, GymContext>, IProductDal
    {
        // Enterprise multi-tenant constructor injection pattern (100.000+ users ready)
        public EfProductDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
        {
        }

        // Enterprise pagination method (memory leak fixed)
        public PaginatedResult<Product> GetAllPaginated(ProductPagingParameters parameters)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var query = _context.Products.Where(x => x.IsActive == true && x.CompanyID == companyId);

            // Enterprise filtering
            if (!string.IsNullOrWhiteSpace(parameters.SearchText))
            {
                query = query.Where(x => x.Name.Contains(parameters.SearchText));
            }

            if (parameters.MinPrice.HasValue)
            {
                query = query.Where(x => x.Price >= parameters.MinPrice.Value);
            }

            if (parameters.MaxPrice.HasValue)
            {
                query = query.Where(x => x.Price <= parameters.MaxPrice.Value);
            }

            if (parameters.IsActive.HasValue)
            {
                query = query.Where(x => x.IsActive == parameters.IsActive.Value);
            }

            // Enterprise sorting optimization
            switch (parameters.SortBy?.ToLower())
            {
                case "name":
                    query = parameters.SortDirection?.ToLower() == "asc"
                        ? query.OrderBy(x => x.Name)
                        : query.OrderByDescending(x => x.Name);
                    break;
                case "price":
                    query = parameters.SortDirection?.ToLower() == "asc"
                        ? query.OrderBy(x => x.Price)
                        : query.OrderByDescending(x => x.Price);
                    break;
                case "creationdate":
                    query = parameters.SortDirection?.ToLower() == "asc"
                        ? query.OrderBy(x => x.CreationDate)
                        : query.OrderByDescending(x => x.CreationDate);
                    break;
                default: // ProductID
                    query = parameters.SortDirection?.ToLower() == "asc"
                        ? query.OrderBy(x => x.ProductID)
                        : query.OrderByDescending(x => x.ProductID);
                    break;
            }

            // Enterprise pagination optimization
            var totalCount = query.Count();

            // Enterprise pagination execution
            var items = query
                .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                .Take(parameters.PageSize)
                .ToList();

            return new PaginatedResult<Product>(items, parameters.PageNumber, parameters.PageSize, totalCount);
        }

        // Enterprise async pagination method (100.000+ users ready)
        public async Task<PaginatedResult<Product>> GetAllPaginatedAsync(ProductPagingParameters parameters)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var query = _context.Products.Where(x => x.IsActive == true && x.CompanyID == companyId);

            // Enterprise filtering
            if (!string.IsNullOrWhiteSpace(parameters.SearchText))
            {
                query = query.Where(x => x.Name.Contains(parameters.SearchText));
            }

            if (parameters.MinPrice.HasValue)
            {
                query = query.Where(x => x.Price >= parameters.MinPrice.Value);
            }

            if (parameters.MaxPrice.HasValue)
            {
                query = query.Where(x => x.Price <= parameters.MaxPrice.Value);
            }

            if (parameters.IsActive.HasValue)
            {
                query = query.Where(x => x.IsActive == parameters.IsActive.Value);
            }

            // Enterprise sorting optimization
            switch (parameters.SortBy?.ToLower())
            {
                case "name":
                    query = parameters.SortDirection?.ToLower() == "asc"
                        ? query.OrderBy(x => x.Name)
                        : query.OrderByDescending(x => x.Name);
                    break;
                case "price":
                    query = parameters.SortDirection?.ToLower() == "asc"
                        ? query.OrderBy(x => x.Price)
                        : query.OrderByDescending(x => x.Price);
                    break;
                case "creationdate":
                    query = parameters.SortDirection?.ToLower() == "asc"
                        ? query.OrderBy(x => x.CreationDate)
                        : query.OrderByDescending(x => x.CreationDate);
                    break;
                default: // ProductID
                    query = parameters.SortDirection?.ToLower() == "asc"
                        ? query.OrderBy(x => x.ProductID)
                        : query.OrderByDescending(x => x.ProductID);
                    break;
            }

            // Enterprise async pagination optimization
            var totalCount = await query.CountAsync();

            // Enterprise async pagination execution
            var items = await query
                .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                .Take(parameters.PageSize)
                .ToListAsync();

            return new PaginatedResult<Product>(items, parameters.PageNumber, parameters.PageSize, totalCount);
        }
    }
}
