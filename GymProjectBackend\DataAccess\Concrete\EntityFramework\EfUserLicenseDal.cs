﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserLicenseDal : EfEntityRepositoryBase<UserLicense, GymContext>, IUserLicenseDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfUserLicenseDal(GymContext context) : base(context)
        {
        }
        // Enterprise license management method (memory leak fixed)
        public List<UserLicenseDto> GetUserLicenseDetails()
        {
            var now = DateTime.Now;
            var result = from ul in _context.UserLicenses
                         join u in _context.Users on ul.UserID equals u.UserID
                         join lp in _context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                         join uc in _context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                         from uc in ucGroup.DefaultIfEmpty()
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                         from c in cGroup.DefaultIfEmpty()
                         where ul.IsActive && ul.EndDate >= now
                         select new UserLicenseDto
                         {
                             UserLicenseID = ul.UserLicenseID,
                             UserID = ul.UserID,
                             UserName = u.FirstName + " " + u.LastName,
                             UserEmail = u.Email,
                             CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                             LicensePackageID = ul.LicensePackageID,
                             PackageName = lp.Name,
                             Role = lp.Role,
                             StartDate = ul.StartDate,
                             EndDate = ul.EndDate,
                             RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                             IsActive = ul.IsActive
                         };

            return result.ToList();
        }

        // Enterprise async license management method (100.000+ users ready)
        public async Task<List<UserLicenseDto>> GetUserLicenseDetailsAsync()
        {
            var now = DateTime.Now;
            var result = from ul in _context.UserLicenses
                         join u in _context.Users on ul.UserID equals u.UserID
                         join lp in _context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                         join uc in _context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                         from uc in ucGroup.DefaultIfEmpty()
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                         from c in cGroup.DefaultIfEmpty()
                         where ul.IsActive && ul.EndDate >= now
                         select new UserLicenseDto
                         {
                             UserLicenseID = ul.UserLicenseID,
                             UserID = ul.UserID,
                             UserName = u.FirstName + " " + u.LastName,
                             UserEmail = u.Email,
                             CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                             LicensePackageID = ul.LicensePackageID,
                             PackageName = lp.Name,
                             Role = lp.Role,
                             StartDate = ul.StartDate,
                             EndDate = ul.EndDate,
                             RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                             IsActive = ul.IsActive
                         };

            return await result.ToListAsync();
        }

        public List<UserLicenseDto> GetActiveUserLicensesByUserId(int userId)
        {
            using (var context = new GymContext())
            {
                var now = DateTime.Now;
                var result = from ul in context.UserLicenses
                             join u in context.Users on ul.UserID equals u.UserID
                             join lp in context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                             join uc in context.UserCompanies on u.UserID equals uc.UserID
                             join c in context.Companies on uc.CompanyId equals c.CompanyID
                             where ul.UserID == userId && ul.IsActive && ul.EndDate >= now
                             select new UserLicenseDto
                             {
                                 UserLicenseID = ul.UserLicenseID,
                                 UserID = ul.UserID,
                                 UserName = u.FirstName + " " + u.LastName,
                                 UserEmail = u.Email,
                                 CompanyName = c.CompanyName,
                                 LicensePackageID = ul.LicensePackageID,
                                 PackageName = lp.Name,
                                 Role = lp.Role,
                                 StartDate = ul.StartDate,
                                 EndDate = ul.EndDate,
                                 RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                                 IsActive = ul.IsActive
                             };

                return result.ToList();
            }
        }

        public UserLicenseDto GetUserLicenseDetail(int userLicenseId)
        {
            using (var context = new GymContext())
            {
                var now = DateTime.Now;
                var result = from ul in context.UserLicenses
                             join u in context.Users on ul.UserID equals u.UserID
                             join lp in context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                             join uc in context.UserCompanies on u.UserID equals uc.UserID
                             join c in context.Companies on uc.CompanyId equals c.CompanyID
                             where ul.UserLicenseID == userLicenseId
                             select new UserLicenseDto
                             {
                                 UserLicenseID = ul.UserLicenseID,
                                 UserID = ul.UserID,
                                 UserName = u.FirstName + " " + u.LastName,
                                 UserEmail = u.Email,
                                 CompanyName = c.CompanyName,
                                 LicensePackageID = ul.LicensePackageID,
                                 PackageName = lp.Name,
                                 Role = lp.Role,
                                 StartDate = ul.StartDate,
                                 EndDate = ul.EndDate,
                                 RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                                 IsActive = ul.IsActive
                             };

                return result.FirstOrDefault();
            }
        }

        public PaginatedUserLicenseDto GetUserLicenseDetailsPaginated(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax)
        {
            using (var context = new GymContext())
            {
                var now = DateTime.Now;
                var query = from ul in context.UserLicenses
                           join u in context.Users on ul.UserID equals u.UserID
                           join lp in context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                           join uc in context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                           from uc in ucGroup.DefaultIfEmpty()
                           join c in context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                           from c in cGroup.DefaultIfEmpty()
                           where ul.IsActive && ul.EndDate >= now
                           select new UserLicenseDto
                           {
                               UserLicenseID = ul.UserLicenseID,
                               UserID = ul.UserID,
                               UserName = u.FirstName + " " + u.LastName,
                               UserEmail = u.Email,
                               CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                               LicensePackageID = ul.LicensePackageID,
                               PackageName = lp.Name,
                               Role = lp.Role,
                               StartDate = ul.StartDate,
                               EndDate = ul.EndDate,
                               RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                               IsActive = ul.IsActive
                           };

                // Search filtering
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(x => x.UserEmail.Contains(searchTerm) || x.CompanyName.Contains(searchTerm));
                }

                // Company name filtering
                if (!string.IsNullOrEmpty(companyName))
                {
                    query = query.Where(x => x.CompanyName.Contains(companyName));
                }

                // Remaining days filtering
                if (remainingDaysMin.HasValue)
                {
                    query = query.Where(x => x.RemainingDays >= remainingDaysMin.Value);
                }

                if (remainingDaysMax.HasValue)
                {
                    query = query.Where(x => x.RemainingDays <= remainingDaysMax.Value);
                }

                // Sorting
                switch (sortBy?.ToLower())
                {
                    case "newest":
                        query = query.OrderByDescending(x => x.StartDate);
                        break;
                    case "oldest":
                        query = query.OrderBy(x => x.StartDate);
                        break;
                    case "expiring":
                        query = query.OrderBy(x => x.RemainingDays);
                        break;
                    case "company":
                        query = query.OrderBy(x => x.CompanyName);
                        break;
                    default:
                        query = query.OrderByDescending(x => x.StartDate);
                        break;
                }

                var totalCount = query.Count();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var data = query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new PaginatedUserLicenseDto
                {
                    Data = data,
                    TotalCount = totalCount,
                    PageNumber = page,
                    PageSize = pageSize,
                    TotalPages = totalPages,
                    HasPreviousPage = page > 1,
                    HasNextPage = page < totalPages
                };
            }
        }

        public PaginatedUserLicenseDto GetExpiredAndPassiveLicenses(int page, int pageSize, string searchTerm)
        {
            using (var context = new GymContext())
            {
                var now = DateTime.Now;
                var query = from ul in context.UserLicenses
                           join u in context.Users on ul.UserID equals u.UserID
                           join lp in context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                           join uc in context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                           from uc in ucGroup.DefaultIfEmpty()
                           join c in context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                           from c in cGroup.DefaultIfEmpty()
                           join cu in context.CompanyUsers on u.UserID equals cu.CompanyUserID into cuGroup
                           from cu in cuGroup.DefaultIfEmpty()
                           join city in context.Cities on cu.CityID equals city.CityID into cityGroup
                           from city in cityGroup.DefaultIfEmpty()
                           join town in context.Towns on cu.TownID equals town.TownID into townGroup
                           from town in townGroup.DefaultIfEmpty()
                           where !ul.IsActive || ul.EndDate < now
                           select new UserLicenseDto
                           {
                               UserLicenseID = ul.UserLicenseID,
                               UserID = ul.UserID,
                               UserName = u.FirstName + " " + u.LastName,
                               UserEmail = u.Email,
                               CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                               LicensePackageID = ul.LicensePackageID,
                               PackageName = lp.Name,
                               Role = lp.Role,
                               StartDate = ul.StartDate,
                               EndDate = ul.EndDate,
                               RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                               IsActive = ul.IsActive
                           };

                // Comprehensive search filtering
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var searchLower = searchTerm.ToLower();
                    query = query.Where(x =>
                        x.UserEmail.ToLower().Contains(searchLower) ||
                        x.CompanyName.ToLower().Contains(searchLower) ||
                        x.UserName.ToLower().Contains(searchLower) ||
                        x.PackageName.ToLower().Contains(searchLower) ||
                        x.Role.ToLower().Contains(searchLower)
                    );
                }

                // Order by most recently expired first
                query = query.OrderByDescending(x => x.EndDate);

                var totalCount = query.Count();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var data = query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new PaginatedUserLicenseDto
                {
                    Data = data,
                    TotalCount = totalCount,
                    PageNumber = page,
                    PageSize = pageSize,
                    TotalPages = totalPages,
                    HasPreviousPage = page > 1,
                    HasNextPage = page < totalPages
                };
            }
        }
    }

}
