﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfTownDal:EfEntityRepositoryBase<Town, GymContext>, ITownDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfTownDal(GymContext context) : base(context)
        {
        }
    }
}
