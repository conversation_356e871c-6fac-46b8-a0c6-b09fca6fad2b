﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDal : EfEntityRepositoryBase<User, GymContext>, IUserDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfUserDal(GymContext context) : base(context)
        {
        }
        // Enterprise security method (memory leak fixed)
        public List<OperationClaim> GetClaims(User user)
        {
            var result = from OperationClaim in _context.OperationClaims
                         join UserOperationClaim in _context.UserOperationClaims
                         on OperationClaim.OperationClaimId equals UserOperationClaim.OperationClaimId
                         where UserOperationClaim.UserId == user.UserID
                         select new OperationClaim { OperationClaimId= OperationClaim.OperationClaimId, Name = OperationClaim.Name };
            return result.ToList();
        }

        // Enterprise async security method (100.000+ users ready)
        public async Task<List<OperationClaim>> GetClaimsAsync(User user)
        {
            var result = from OperationClaim in _context.OperationClaims
                         join UserOperationClaim in _context.UserOperationClaims
                         on OperationClaim.OperationClaimId equals UserOperationClaim.OperationClaimId
                         where UserOperationClaim.UserId == user.UserID
                         select new OperationClaim { OperationClaimId= OperationClaim.OperationClaimId, Name = OperationClaim.Name };
            return await result.ToListAsync();
        }

        /// <summary>
        /// Enterprise method: Member rolü olmayan tüm kullanıcıları getirir (memory leak fixed)
        /// </summary>
        public List<User> GetNonMembers()
        {
            // Enterprise role filtering (Cache'lenebilir)
            var memberRoleId = _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefault();

            if (memberRoleId == 0)
            {
                // Enterprise fallback - tüm aktif kullanıcıları döner
                return _context.Users
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .ToList();
            }

            // Enterprise performance optimization - HashSet kullanımı
            var usersWithMemberRole = _context.UserOperationClaims
                .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                .Select(uoc => uoc.UserId)
                .ToHashSet(); // HashSet performans için

            // Enterprise filtering execution
            return _context.Users
                .Where(u => u.IsActive && !usersWithMemberRole.Contains(u.UserID))
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToList();
        }

        /// <summary>
        /// Enterprise async method: Member rolü olmayan tüm kullanıcıları getirir (100.000+ users ready)
        /// </summary>
        public async Task<List<User>> GetNonMembersAsync()
        {
            // Enterprise async role filtering (Cache'lenebilir)
            var memberRoleId = await _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefaultAsync();

            if (memberRoleId == 0)
            {
                // Enterprise async fallback - tüm aktif kullanıcıları döner
                return await _context.Users
                    .Where(u => u.IsActive)
                    .OrderBy(u => u.FirstName)
                    .ThenBy(u => u.LastName)
                    .ToListAsync();
            }

            // Enterprise async performance optimization - HashSet kullanımı
            var usersWithMemberRole = await _context.UserOperationClaims
                .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                .Select(uoc => uoc.UserId)
                .ToListAsync();

            var memberRoleHashSet = usersWithMemberRole.ToHashSet(); // HashSet performans için

            // Enterprise async filtering execution
            return await _context.Users
                .Where(u => u.IsActive && !memberRoleHashSet.Contains(u.UserID))
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToListAsync();
        }

        /// <summary>
        /// Enterprise pagination method: Member rolü olmayan kullanıcıları sayfalı olarak getirir (memory leak fixed)
        /// </summary>
        public List<User> GetNonMembersPaginated(int page, int pageSize, string searchTerm)
        {
            // Enterprise role filtering
            var memberRoleId = _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefault();

            var query = _context.Users.AsQueryable();

            // Enterprise active user filtering
            query = query.Where(u => u.IsActive);

            // Enterprise member role filtering
            if (memberRoleId > 0)
            {
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId);

                query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
            }

            // Enterprise search filtering
            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(u =>
                    u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                    u.LastName.ToLower().Contains(lowerSearchTerm) ||
                    u.Email.ToLower().Contains(lowerSearchTerm));
            }

            // Enterprise pagination and sorting
            return query
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        /// <summary>
        /// Enterprise async pagination method: Member rolü olmayan kullanıcıları sayfalı olarak getirir (100.000+ users ready)
        /// </summary>
        public async Task<List<User>> GetNonMembersPaginatedAsync(int page, int pageSize, string searchTerm)
        {
            // Enterprise async role filtering
            var memberRoleId = await _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefaultAsync();

            var query = _context.Users.AsQueryable();

            // Enterprise active user filtering
            query = query.Where(u => u.IsActive);

            // Enterprise member role filtering
            if (memberRoleId > 0)
            {
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId);

                query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
            }

            // Enterprise search filtering
            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(u =>
                    u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                    u.LastName.ToLower().Contains(lowerSearchTerm) ||
                    u.Email.ToLower().Contains(lowerSearchTerm));
            }

            // Enterprise async pagination and sorting
            return await query
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        /// <summary>
        /// Enterprise count method: Member rolü olmayan kullanıcı sayısını getirir (memory leak fixed)
        /// </summary>
        public int GetNonMembersCount(string searchTerm)
        {
            // Enterprise role filtering
            var memberRoleId = _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefault();

            var query = _context.Users.AsQueryable();

            // Enterprise active user filtering
            query = query.Where(u => u.IsActive);

            // Enterprise member role filtering
            if (memberRoleId > 0)
            {
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId);

                query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
            }

            // Enterprise search filtering
            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(u =>
                    u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                    u.LastName.ToLower().Contains(lowerSearchTerm) ||
                    u.Email.ToLower().Contains(lowerSearchTerm));
            }

            return query.Count();
        }

        /// <summary>
        /// Enterprise async count method: Member rolü olmayan kullanıcı sayısını getirir (100.000+ users ready)
        /// </summary>
        public async Task<int> GetNonMembersCountAsync(string searchTerm)
        {
            // Enterprise async role filtering
            var memberRoleId = await _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefaultAsync();

            var query = _context.Users.AsQueryable();

            // Enterprise active user filtering
            query = query.Where(u => u.IsActive);

            // Enterprise member role filtering
            if (memberRoleId > 0)
            {
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId);

                query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
            }

            // Enterprise search filtering
            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(u =>
                    u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                    u.LastName.ToLower().Contains(lowerSearchTerm) ||
                    u.Email.ToLower().Contains(lowerSearchTerm));
            }

            return await query.CountAsync();
        }
       
    }
}
