﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyAdressDal : EfEntityRepositoryBase<CompanyAdress, GymContext>, ICompanyAdressDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfCompanyAdressDal(GymContext context) : base(context)
        {
        }

        // Enterprise method (memory leak fixed)
        public List<CompanyAdressDetailDto> GetCompanyAdressDetails()
        {
            var result = from ca in _context.CompanyAdresses
                         join c in _context.Companies on ca.CompanyID equals c.CompanyID
                         join ci in _context.Cities on ca.CityID equals ci.CityID
                         join t in _context.Towns on ca.TownID equals t.TownID
                         where ca.IsActive==true
                         select new CompanyAdressDetailDto
                         {
                             CompanyAdressID = ca.CompanyAdressID,
                             CompanyName = c.CompanyName,
                             CityName = ci.CityName,
                             TownName = t.TownName,
                             Adress = ca.Adress,
                             isActive = ca.IsActive
                         };
            return result.ToList();
        }

        // Enterprise async method (100.000+ users ready)
        public async Task<List<CompanyAdressDetailDto>> GetCompanyAdressDetailsAsync()
        {
            var result = from ca in _context.CompanyAdresses
                         join c in _context.Companies on ca.CompanyID equals c.CompanyID
                         join ci in _context.Cities on ca.CityID equals ci.CityID
                         join t in _context.Towns on ca.TownID equals t.TownID
                         where ca.IsActive==true
                         select new CompanyAdressDetailDto
                         {
                             CompanyAdressID = ca.CompanyAdressID,
                             CompanyName = c.CompanyName,
                             CityName = ci.CityName,
                             TownName = t.TownName,
                             Adress = ca.Adress,
                             isActive = ca.IsActive
                         };
            return await result.ToListAsync();
        }
    }
}
