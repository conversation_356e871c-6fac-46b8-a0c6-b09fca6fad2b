﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipFreezeHistoryDal : EfCompanyEntityRepositoryBase<MembershipFreezeHistory, GymContext>, IMembershipFreezeHistoryDal
    {
        // Enterprise multi-tenant constructor injection pattern (100.000+ users ready)
        public EfMembershipFreezeHistoryDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
        {
        }
        // Enterprise multi-tenant method (memory leak fixed)
        public List<MembershipFreezeHistoryDto> GetFreezeHistoryDetails()
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var result = from fh in _context.MembershipFreezeHistory
                         join m in _context.Memberships on fh.MembershipID equals m.MembershipID
                         join mem in _context.Members on m.MemberID equals mem.MemberID
                         join mt in _context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                         where fh.CompanyID == companyId // Enterprise multi-tenant filtering
                         && m.CompanyID == companyId // Enterprise multi-tenant security
                         && mem.CompanyID == companyId // Enterprise multi-tenant security
                         && mt.CompanyID == companyId // Enterprise multi-tenant security
                         orderby fh.CreationDate descending // Enterprise performance optimization
                         select new MembershipFreezeHistoryDto
                         {
                             FreezeHistoryID = fh.FreezeHistoryID,
                             MemberName = mem.Name ?? "",
                             PhoneNumber = mem.PhoneNumber ?? "",
                             Branch = mt.Branch ?? "",
                             StartDate = fh.StartDate,
                             PlannedEndDate = fh.PlannedEndDate,
                             ActualEndDate = fh.ActualEndDate,
                             FreezeDays = fh.FreezeDays,
                             UsedDays = fh.UsedDays,
                             CancellationType = fh.CancellationType ?? "",
                             CreationDate = fh.CreationDate
                         };

            return result.ToList();
        }

        // Enterprise async multi-tenant method (100.000+ users ready)
        public async Task<List<MembershipFreezeHistoryDto>> GetFreezeHistoryDetailsAsync()
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var result = from fh in _context.MembershipFreezeHistory
                         join m in _context.Memberships on fh.MembershipID equals m.MembershipID
                         join mem in _context.Members on m.MemberID equals mem.MemberID
                         join mt in _context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                         where fh.CompanyID == companyId // Enterprise multi-tenant filtering
                         && m.CompanyID == companyId // Enterprise multi-tenant security
                         && mem.CompanyID == companyId // Enterprise multi-tenant security
                         && mt.CompanyID == companyId // Enterprise multi-tenant security
                         orderby fh.CreationDate descending // Enterprise performance optimization
                         select new MembershipFreezeHistoryDto
                         {
                             FreezeHistoryID = fh.FreezeHistoryID,
                             MemberName = mem.Name ?? "",
                             PhoneNumber = mem.PhoneNumber ?? "",
                             Branch = mt.Branch ?? "",
                             StartDate = fh.StartDate,
                             PlannedEndDate = fh.PlannedEndDate,
                             ActualEndDate = fh.ActualEndDate,
                             FreezeDays = fh.FreezeDays,
                             UsedDays = fh.UsedDays,
                             CancellationType = fh.CancellationType ?? "",
                             CreationDate = fh.CreationDate
                         };

            return await result.ToListAsync();
        }

        // Enterprise multi-tenant method (memory leak fixed)
        public List<MembershipFreezeHistoryDto> GetFreezeHistoryByMembershipId(int membershipId)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var result = from fh in _context.MembershipFreezeHistory
                         join m in _context.Memberships on fh.MembershipID equals m.MembershipID
                         join mem in _context.Members on m.MemberID equals mem.MemberID
                         join mt in _context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                         where fh.MembershipID == membershipId
                         && fh.CompanyID == companyId // Enterprise multi-tenant filtering
                         && m.CompanyID == companyId // Enterprise multi-tenant security
                         && mem.CompanyID == companyId // Enterprise multi-tenant security
                         && mt.CompanyID == companyId // Enterprise multi-tenant security
                         orderby fh.CreationDate descending // Enterprise performance optimization
                         select new MembershipFreezeHistoryDto
                         {
                             FreezeHistoryID = fh.FreezeHistoryID,
                             MemberName = mem.Name ?? "",
                             PhoneNumber = mem.PhoneNumber ?? "",
                             Branch = mt.Branch ?? "",
                             StartDate = fh.StartDate,
                             PlannedEndDate = fh.PlannedEndDate,
                             ActualEndDate = fh.ActualEndDate,
                             FreezeDays = fh.FreezeDays,
                             UsedDays = fh.UsedDays,
                             CancellationType = fh.CancellationType ?? "",
                             CreationDate = fh.CreationDate
                         };

            return result.ToList();
        }

        // Enterprise async multi-tenant method (100.000+ users ready)
        public async Task<List<MembershipFreezeHistoryDto>> GetFreezeHistoryByMembershipIdAsync(int membershipId)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var result = from fh in _context.MembershipFreezeHistory
                         join m in _context.Memberships on fh.MembershipID equals m.MembershipID
                         join mem in _context.Members on m.MemberID equals mem.MemberID
                         join mt in _context.MembershipTypes on m.MembershipTypeID equals mt.MembershipTypeID
                         where fh.MembershipID == membershipId
                         && fh.CompanyID == companyId // Enterprise multi-tenant filtering
                         && m.CompanyID == companyId // Enterprise multi-tenant security
                         && mem.CompanyID == companyId // Enterprise multi-tenant security
                         && mt.CompanyID == companyId // Enterprise multi-tenant security
                         orderby fh.CreationDate descending // Enterprise performance optimization
                         select new MembershipFreezeHistoryDto
                         {
                             FreezeHistoryID = fh.FreezeHistoryID,
                             MemberName = mem.Name ?? "",
                             PhoneNumber = mem.PhoneNumber ?? "",
                             Branch = mt.Branch ?? "",
                             StartDate = fh.StartDate,
                             PlannedEndDate = fh.PlannedEndDate,
                             ActualEndDate = fh.ActualEndDate,
                             FreezeDays = fh.FreezeDays,
                             UsedDays = fh.UsedDays,
                             CancellationType = fh.CancellationType ?? "",
                             CreationDate = fh.CreationDate
                         };

            return await result.ToListAsync();
        }

        // Enterprise calculation method (memory leak fixed)
        public int GetTotalFreezeDaysUsedInLastYear(int membershipId)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var oneYearAgo = DateTime.Now.AddYears(-1);
            return _context.MembershipFreezeHistory
                .Where(fh => fh.MembershipID == membershipId &&
                       fh.CreationDate >= oneYearAgo &&
                       fh.CompanyID == companyId) // Enterprise multi-tenant filtering
                .Sum(fh => fh.UsedDays ?? fh.FreezeDays);
        }

        // Enterprise async calculation method (100.000+ users ready)
        public async Task<int> GetTotalFreezeDaysUsedInLastYearAsync(int membershipId)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var oneYearAgo = DateTime.Now.AddYears(-1);
            return await _context.MembershipFreezeHistory
                .Where(fh => fh.MembershipID == membershipId &&
                       fh.CreationDate >= oneYearAgo &&
                       fh.CompanyID == companyId) // Enterprise multi-tenant filtering
                .SumAsync(fh => fh.UsedDays ?? fh.FreezeDays);
        }
    }
}