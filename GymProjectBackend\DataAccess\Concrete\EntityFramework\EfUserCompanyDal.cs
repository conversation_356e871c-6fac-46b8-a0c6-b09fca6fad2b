﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserCompanyDal: EfEntityRepositoryBase<UserCompany, GymContext>, IUserCompanyDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfUserCompanyDal(GymContext context) : base(context)
        {
        }

        // Enterprise method (memory leak fixed)
        public List<UserCompanyDetailDto> GetUserCompanyDetails()
        {
            var result = from uc in _context.UserCompanies
                         join cu in _context.CompanyUsers on uc.UserID equals cu.CompanyUserID
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID
                         where uc.IsActive == true
                         select new UserCompanyDetailDto
                         {
                             UserCompanyId = uc.UserCompanyID,
                             CompanyUserName = cu.Name,
                             CompanyName = c.CompanyName,
                             isActive=uc.IsActive,
                         };
            return result.ToList();
        }

        // Enterprise async method (100.000+ users ready)
        public async Task<List<UserCompanyDetailDto>> GetUserCompanyDetailsAsync()
        {
            var result = from uc in _context.UserCompanies
                         join cu in _context.CompanyUsers on uc.UserID equals cu.CompanyUserID
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID
                         where uc.IsActive == true
                         select new UserCompanyDetailDto
                         {
                             UserCompanyId = uc.UserCompanyID,
                             CompanyUserName = cu.Name,
                             CompanyName = c.CompanyName,
                             isActive=uc.IsActive,
                         };
            return await result.ToListAsync();
        }

        // Enterprise method (memory leak fixed)
        public int GetUserCompanyId(int userId)
        {
            // Mevcut veritabanı tasarımına göre: UserCompany.UserID = CompanyUser.CompanyUserID
            // Bu yüzden önce User'ın email'i ile CompanyUser'ı bulup, sonra UserCompany'yi arıyoruz
            var user = _context.Users.FirstOrDefault(u => u.UserID == userId);
            if (user == null) return -1;

            var companyUser = _context.CompanyUsers.FirstOrDefault(cu => cu.Email == user.Email);
            if (companyUser == null) return -1;

            var userCompany = _context.UserCompanies
                .FirstOrDefault(uc => uc.UserID == companyUser.CompanyUserID && uc.IsActive == true);

            return userCompany?.CompanyId ?? -1;
        }

        // Enterprise async method (100.000+ users ready)
        public async Task<int> GetUserCompanyIdAsync(int userId)
        {
            // Mevcut veritabanı tasarımına göre: UserCompany.UserID = CompanyUser.CompanyUserID
            // Bu yüzden önce User'ın email'i ile CompanyUser'ı bulup, sonra UserCompany'yi arıyoruz
            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserID == userId);
            if (user == null) return -1;

            var companyUser = await _context.CompanyUsers.FirstOrDefaultAsync(cu => cu.Email == user.Email);
            if (companyUser == null) return -1;

            var userCompany = await _context.UserCompanies
                .FirstOrDefaultAsync(uc => uc.UserID == companyUser.CompanyUserID && uc.IsActive == true);

            return userCompany?.CompanyId ?? -1;
        }
    }
}
