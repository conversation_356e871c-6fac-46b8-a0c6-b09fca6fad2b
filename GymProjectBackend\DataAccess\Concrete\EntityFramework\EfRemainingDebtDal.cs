﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfRemainingDebtDal : EfCompanyEntityRepositoryBase<RemainingDebt, GymContext>, IRemainingDebtDal
    {
        // Enterprise multi-tenant constructor injection pattern (100.000+ users ready)
        public EfRemainingDebtDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
        {
        }

        // Enterprise multi-tenant method (memory leak fixed)
        public List<RemainingDebtDetailDto> GetRemainingDebtDetails()
        {
            // Enterprise multi-tenant security (inherited from base class)
            int companyId = _companyContext.GetCompanyId();

            var result = from rd in _context.RemainingDebts
                         join p in _context.Payments on rd.PaymentID equals p.PaymentID
                         join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                         join m in _context.Members on ms.MemberID equals m.MemberID
                         where rd.IsActive && rd.RemainingAmount > 0
                         && rd.CompanyID == companyId // Enterprise multi-tenant filtering
                         && p.CompanyID == companyId // Enterprise multi-tenant security
                         && ms.CompanyID == companyId // Enterprise multi-tenant security
                         && m.CompanyID == companyId // Enterprise multi-tenant security
                         select new RemainingDebtDetailDto
                         {
                             RemainingDebtID = rd.RemainingDebtID,
                             PaymentID = rd.PaymentID,
                             MemberName = m.Name,
                             PhoneNumber = m.PhoneNumber,
                             OriginalAmount = rd.OriginalAmount,
                             RemainingAmount = rd.RemainingAmount,
                             LastUpdateDate = rd.LastUpdateDate,
                             PaymentMethod = p.PaymentMethod
                         };
            return result.ToList();
        }

        // Enterprise async multi-tenant method (100.000+ users ready)
        public async Task<List<RemainingDebtDetailDto>> GetRemainingDebtDetailsAsync()
        {
            // Enterprise multi-tenant security (inherited from base class)
            int companyId = _companyContext.GetCompanyId();

            var result = from rd in _context.RemainingDebts
                         join p in _context.Payments on rd.PaymentID equals p.PaymentID
                         join ms in _context.Memberships on p.MemberShipID equals ms.MembershipID
                         join m in _context.Members on ms.MemberID equals m.MemberID
                         where rd.IsActive && rd.RemainingAmount > 0
                         && rd.CompanyID == companyId // Enterprise multi-tenant filtering
                         && p.CompanyID == companyId // Enterprise multi-tenant security
                         && ms.CompanyID == companyId // Enterprise multi-tenant security
                         && m.CompanyID == companyId // Enterprise multi-tenant security
                         select new RemainingDebtDetailDto
                         {
                             RemainingDebtID = rd.RemainingDebtID,
                             PaymentID = rd.PaymentID,
                             MemberName = m.Name,
                             PhoneNumber = m.PhoneNumber,
                             OriginalAmount = rd.OriginalAmount,
                             RemainingAmount = rd.RemainingAmount,
                             LastUpdateDate = rd.LastUpdateDate,
                             PaymentMethod = p.PaymentMethod
                         };
            return await result.ToListAsync();
        }
    }
}
