﻿using Core.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Core.DataAccess.EntityFramework
{
    public class EfEntityRepositoryBase<TEntity, TContext> : IEntityRepository<TEntity>
        where TEntity : class, IEntity, new()
        where TContext : DbContext
    {
        protected readonly TContext _context;

        // Enterprise constructor injection pattern
        public EfEntityRepositoryBase(TContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }


        // Enterprise sync Add method (memory leak fixed)
        public void Add(TEntity entity)
        {
            var addedEntity = _context.Entry(entity);
            addedEntity.State = EntityState.Added;

            // Enterprise audit fields management
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                addedEntity.Property("CreationDate").CurrentValue = DateTime.Now;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                addedEntity.Property("DeletedDate").IsModified = false;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                addedEntity.Property("UpdatedDate").IsModified = false; // Fixed: was DeletedDate
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                addedEntity.Property("IsActive").CurrentValue = true;
            }

            _context.SaveChanges();
        }

        // Enterprise async Add method (100.000+ users ready)
        public async Task AddAsync(TEntity entity)
        {
            var addedEntity = _context.Entry(entity);
            addedEntity.State = EntityState.Added;

            // Enterprise audit fields management
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                addedEntity.Property("CreationDate").CurrentValue = DateTime.Now;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                addedEntity.Property("DeletedDate").IsModified = false;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                addedEntity.Property("UpdatedDate").IsModified = false;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                addedEntity.Property("IsActive").CurrentValue = true;
            }

            await _context.SaveChangesAsync();
        }

        // Enterprise soft delete (memory leak fixed)
        public void Delete(object id)
        {
            TEntity entity = _context.Set<TEntity>().Find(id);
            if (entity == null) return;

            var deletedEntity = _context.Entry(entity);
            deletedEntity.State = EntityState.Modified;

            // Enterprise audit fields management
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                deletedEntity.Property("CreationDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                deletedEntity.Property("DeletedDate").CurrentValue = DateTime.Now;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                deletedEntity.Property("UpdatedDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                deletedEntity.Property("IsActive").CurrentValue = false;
            }

            _context.SaveChanges();
        }

        // Enterprise async soft delete (100.000+ users ready)
        public async Task DeleteAsync(object id)
        {
            TEntity entity = await _context.Set<TEntity>().FindAsync(id);
            if (entity == null) return;

            var deletedEntity = _context.Entry(entity);
            deletedEntity.State = EntityState.Modified;

            // Enterprise audit fields management
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                deletedEntity.Property("CreationDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                deletedEntity.Property("DeletedDate").CurrentValue = DateTime.Now;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                deletedEntity.Property("UpdatedDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                deletedEntity.Property("IsActive").CurrentValue = false;
            }

            await _context.SaveChangesAsync();
        }

        // Enterprise hard delete (memory leak fixed)
        public void HardDelete(object id)
        {
            TEntity entity = _context.Set<TEntity>().Find(id);
            if (entity == null) return;

            var deletedEntity = _context.Entry(entity);
            deletedEntity.State = EntityState.Deleted;

            _context.SaveChanges();
        }

        // Enterprise async hard delete (100.000+ users ready)
        public async Task HardDeleteAsync(object id)
        {
            TEntity entity = await _context.Set<TEntity>().FindAsync(id);
            if (entity == null) return;

            var deletedEntity = _context.Entry(entity);
            deletedEntity.State = EntityState.Deleted;

            await _context.SaveChangesAsync();
        }




        // Enterprise Get method (memory leak fixed)
        public TEntity Get(Expression<Func<TEntity, bool>> filter)
        {
            return _context.Set<TEntity>().SingleOrDefault(filter);
        }

        // Enterprise async Get method (100.000+ users ready)
        public async Task<TEntity> GetAsync(Expression<Func<TEntity, bool>> filter)
        {
            return await _context.Set<TEntity>().SingleOrDefaultAsync(filter);
        }

        // Enterprise GetAll method (memory leak fixed)
        public List<TEntity> GetAll(Expression<Func<TEntity, bool>> filter = null)
        {
            return filter == null
                ? _context.Set<TEntity>().ToList()
                : _context.Set<TEntity>().Where(filter).ToList();
        }

        // Enterprise async GetAll method (100.000+ users ready)
        public async Task<List<TEntity>> GetAllAsync(Expression<Func<TEntity, bool>> filter = null)
        {
            return filter == null
                ? await _context.Set<TEntity>().ToListAsync()
                : await _context.Set<TEntity>().Where(filter).ToListAsync();
        }

        // Enterprise Update method (memory leak fixed)
        public void Update(TEntity entity)
        {
            var updatedEntity = _context.Entry(entity);
            updatedEntity.State = EntityState.Modified;

            // Enterprise audit fields management
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                updatedEntity.Property("CreationDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                updatedEntity.Property("DeletedDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                updatedEntity.Property("UpdatedDate").CurrentValue = DateTime.Now;
            }

            _context.SaveChanges();
        }

        // Enterprise async Update method (100.000+ users ready)
        public async Task UpdateAsync(TEntity entity)
        {
            var updatedEntity = _context.Entry(entity);
            updatedEntity.State = EntityState.Modified;

            // Enterprise audit fields management
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                updatedEntity.Property("CreationDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                updatedEntity.Property("DeletedDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                updatedEntity.Property("UpdatedDate").CurrentValue = DateTime.Now;
            }

            await _context.SaveChangesAsync();
        }
    }
}
