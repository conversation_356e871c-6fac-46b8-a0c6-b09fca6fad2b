using Core.Entities;
using Core.Utilities.Security.CompanyContext;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Core.DataAccess.EntityFramework
{
    public class EfCompanyEntityRepositoryBase<TEntity, TContext> : EfEntityRepositoryBase<TEntity, TContext>, IEntityRepository<TEntity>
        where TEntity : class, ICompanyEntity, new()
        where TContext : DbContext
    {
        protected readonly ICompanyContext _companyContext;

        // Enterprise constructor injection pattern (multi-tenant ready)
        public EfCompanyEntityRepositoryBase(TContext context, ICompanyContext companyContext)
            : base(context)
        {
            _companyContext = companyContext ?? throw new ArgumentNullException(nameof(companyContext));
        }

        // Enterprise multi-tenant GetAll (memory leak fixed)
        public new List<TEntity> GetAll(Expression<Func<TEntity, bool>> filter = null)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, boş liste döndür (security)
            if (companyId <= 0)
            {
                return new List<TEntity>();
            }

            // Enterprise multi-tenant filtering
            var query = _context.Set<TEntity>().Where(e => e.CompanyID == companyId);

            // Eğer ek filtre varsa, uygula
            if (filter != null)
            {
                query = query.Where(filter);
            }

            return query.ToList();
        }

        // Enterprise async multi-tenant GetAll (100.000+ users ready)
        public new async Task<List<TEntity>> GetAllAsync(Expression<Func<TEntity, bool>> filter = null)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, boş liste döndür (security)
            if (companyId <= 0)
            {
                return new List<TEntity>();
            }

            // Enterprise multi-tenant filtering
            var query = _context.Set<TEntity>().Where(e => e.CompanyID == companyId);

            // Eğer ek filtre varsa, uygula
            if (filter != null)
            {
                query = query.Where(filter);
            }

            return await query.ToListAsync();
        }

        // Enterprise multi-tenant Get (memory leak fixed)
        public new TEntity Get(Expression<Func<TEntity, bool>> filter)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, null döndür (security)
            if (companyId <= 0)
            {
                return null;
            }

            // Enterprise multi-tenant filtering
            return _context.Set<TEntity>()
                .Where(e => e.CompanyID == companyId)
                .SingleOrDefault(filter);
        }

        // Enterprise async multi-tenant Get (100.000+ users ready)
        public new async Task<TEntity> GetAsync(Expression<Func<TEntity, bool>> filter)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, null döndür (security)
            if (companyId <= 0)
            {
                return null;
            }

            // Enterprise multi-tenant filtering
            return await _context.Set<TEntity>()
                .Where(e => e.CompanyID == companyId)
                .SingleOrDefaultAsync(filter);
        }

        // Enterprise multi-tenant Add (security enhanced)
        public new void Add(TEntity entity)
        {
            // Enterprise multi-tenant security validation
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, işlemi iptal et (security)
            if (companyId <= 0)
            {
                throw new UnauthorizedAccessException("Geçerli bir şirket ID'si bulunamadı. Multi-tenant güvenlik ihlali!");
            }

            // Enterprise multi-tenant data isolation
            entity.CompanyID = companyId;

            // Temel sınıfın Add metodunu çağır (memory leak fixed)
            base.Add(entity);
        }

        // Enterprise async multi-tenant Add (100.000+ users ready)
        public new async Task AddAsync(TEntity entity)
        {
            // Enterprise multi-tenant security validation
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, işlemi iptal et (security)
            if (companyId <= 0)
            {
                throw new UnauthorizedAccessException("Geçerli bir şirket ID'si bulunamadı. Multi-tenant güvenlik ihlali!");
            }

            // Enterprise multi-tenant data isolation
            entity.CompanyID = companyId;

            // Temel sınıfın AddAsync metodunu çağır (memory leak fixed)
            await base.AddAsync(entity);
        }

        // Enterprise multi-tenant Update (security enhanced)
        public new void Update(TEntity entity)
        {
            // Enterprise multi-tenant security validation
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, işlemi iptal et (security)
            if (companyId <= 0)
            {
                throw new UnauthorizedAccessException("Geçerli bir şirket ID'si bulunamadı. Multi-tenant güvenlik ihlali!");
            }

            // Enterprise multi-tenant data isolation
            entity.CompanyID = companyId;

            // Temel sınıfın Update metodunu çağır (memory leak fixed)
            base.Update(entity);
        }

        // Enterprise async multi-tenant Update (100.000+ users ready)
        public new async Task UpdateAsync(TEntity entity)
        {
            // Enterprise multi-tenant security validation
            int companyId = _companyContext.GetCompanyId();

            // Eğer şirket ID'si geçerli değilse, işlemi iptal et (security)
            if (companyId <= 0)
            {
                throw new UnauthorizedAccessException("Geçerli bir şirket ID'si bulunamadı. Multi-tenant güvenlik ihlali!");
            }

            // Enterprise multi-tenant data isolation
            entity.CompanyID = companyId;

            // Temel sınıfın UpdateAsync metodunu çağır (memory leak fixed)
            await base.UpdateAsync(entity);
        }
    }
}
