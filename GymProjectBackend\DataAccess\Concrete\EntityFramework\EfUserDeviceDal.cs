﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using DataAccess.Abstract;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDeviceDal : EfEntityRepositoryBase<UserDevice, GymContext>, IUserDeviceDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfUserDeviceDal(GymContext context) : base(context)
        {
        }

        // Enterprise method (memory leak fixed)
        public List<UserDevice> GetActiveDevicesByUserId(int userId)
        {
            return _context.UserDevices
                .Where(d => d.UserId == userId && d.IsActive)
                .ToList();
        }

        // Enterprise method (memory leak fixed)
        public UserDevice GetByRefreshToken(string refreshToken)
        {
            return _context.UserDevices
                .FirstOrDefault(d => d.RefreshToken == refreshToken && d.IsActive);
        }

        // Enterprise async methods (100.000+ users ready)
        public async Task<List<UserDevice>> GetActiveDevicesByUserIdAsync(int userId)
        {
            return await _context.UserDevices
                .Where(d => d.UserId == userId && d.IsActive)
                .ToListAsync();
        }

        public async Task<UserDevice> GetByRefreshTokenAsync(string refreshToken)
        {
            return await _context.UserDevices
                .FirstOrDefaultAsync(d => d.RefreshToken == refreshToken && d.IsActive);
        }
    }
}
