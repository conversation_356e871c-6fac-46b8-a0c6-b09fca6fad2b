﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyDal : EfEntityRepositoryBase<Company, GymContext>, ICompanyDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfCompanyDal(GymContext context) : base(context)
        {
        }

        // Enterprise method (memory leak fixed)
        public List<ActiveCompanyDetailDto> GetActiveCompanies()
        {
            var result = from c in _context.Companies
                         where c.IsActive == true
                         select new ActiveCompanyDetailDto
                         {
                           CompanyID = c.CompanyID,
                           CompanyName = c.CompanyName,
                           PhoneNumber = c.PhoneNumber,
                           IsActive = c.IsActive,

                         };
            return result.ToList();
        }

        // Enterprise async method (100.000+ users ready)
        public async Task<List<ActiveCompanyDetailDto>> GetActiveCompaniesAsync()
        {
            var result = from c in _context.Companies
                         where c.IsActive == true
                         select new ActiveCompanyDetailDto
                         {
                           CompanyID = c.CompanyID,
                           CompanyName = c.CompanyName,
                           PhoneNumber = c.PhoneNumber,
                           IsActive = c.IsActive,

                         };
            return await result.ToListAsync();
        }
    }
}
