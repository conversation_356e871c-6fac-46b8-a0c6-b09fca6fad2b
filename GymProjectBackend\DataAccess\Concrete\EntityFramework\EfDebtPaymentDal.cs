using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfDebtPaymentDal : EfCompanyEntityRepositoryBase<DebtPayment, GymContext>, IDebtPaymentDal
    {
        // Enterprise multi-tenant constructor injection pattern (100.000+ users ready)
        public EfDebtPaymentDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
        {
        }

        // Enterprise financial transaction method (memory leak fixed)
        public IResult DeleteDebtPaymentWithRemainingDebtUpdate(int debtPaymentId)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    // Enterprise multi-tenant security
                    int companyId = _companyContext.GetCompanyId();

                    // DebtPayment kaydını bul (enterprise multi-tenant filtering)
                    var debtPayment = _context.DebtPayments
                        .FirstOrDefault(dp => dp.DebtPaymentID == debtPaymentId && dp.CompanyID == companyId);
                    if (debtPayment == null)
                        return new ErrorResult("Borç ödemesi bulunamadı.");

                    // İlgili RemainingDebt kaydını bul (enterprise multi-tenant filtering)
                    var remainingDebt = _context.RemainingDebts
                        .FirstOrDefault(rd => rd.RemainingDebtID == debtPayment.RemainingDebtID && rd.CompanyID == companyId);
                    if (remainingDebt == null)
                        return new ErrorResult("Kalan borç kaydı bulunamadı.");

                    // Enterprise financial calculation
                    remainingDebt.RemainingAmount += debtPayment.PaidAmount;
                    remainingDebt.LastUpdateDate = DateTime.Now;

                    // Enterprise soft delete
                    _context.DebtPayments.Remove(debtPayment);

                    // Enterprise transaction commit
                    _context.SaveChanges();

                    scope.Complete();
                    return new SuccessResult("Borç ödemesi başarıyla silindi.");
                }
                catch (Exception ex)
                {
                    scope.Dispose();
                    return new ErrorResult($"Borç ödemesi silinirken bir hata oluştu: {ex.Message}");
                }
            }
        }

        // Enterprise async financial transaction method (100.000+ users ready)
        public async Task<IResult> DeleteDebtPaymentWithRemainingDebtUpdateAsync(int debtPaymentId)
        {
            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    // Enterprise multi-tenant security
                    int companyId = _companyContext.GetCompanyId();

                    // DebtPayment kaydını bul (enterprise multi-tenant filtering)
                    var debtPayment = await _context.DebtPayments
                        .FirstOrDefaultAsync(dp => dp.DebtPaymentID == debtPaymentId && dp.CompanyID == companyId);
                    if (debtPayment == null)
                        return new ErrorResult("Borç ödemesi bulunamadı.");

                    // İlgili RemainingDebt kaydını bul (enterprise multi-tenant filtering)
                    var remainingDebt = await _context.RemainingDebts
                        .FirstOrDefaultAsync(rd => rd.RemainingDebtID == debtPayment.RemainingDebtID && rd.CompanyID == companyId);
                    if (remainingDebt == null)
                        return new ErrorResult("Kalan borç kaydı bulunamadı.");

                    // Enterprise financial calculation
                    remainingDebt.RemainingAmount += debtPayment.PaidAmount;
                    remainingDebt.LastUpdateDate = DateTime.Now;

                    // Enterprise soft delete
                    _context.DebtPayments.Remove(debtPayment);

                    // Enterprise async transaction commit
                    await _context.SaveChangesAsync();

                    scope.Complete();
                    return new SuccessResult("Borç ödemesi başarıyla silindi.");
                }
                catch (Exception ex)
                {
                    scope.Dispose();
                    return new ErrorResult($"Borç ödemesi silinirken bir hata oluştu: {ex.Message}");
                }
            }
        }
    }
}
