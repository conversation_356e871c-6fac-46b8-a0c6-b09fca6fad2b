using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfExerciseCategoryDal : EfEntityRepositoryBase<ExerciseCategory, GymContext>, IExerciseCategoryDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfExerciseCategoryDal(GymContext context) : base(context)
        {
        }

        // Enterprise method (memory leak fixed)
        public List<ExerciseCategoryDto> GetAllCategories()
        {
            var result = from ec in _context.ExerciseCategories
                         select new ExerciseCategoryDto
                         {
                             ExerciseCategoryID = ec.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             Description = ec.Description,
                             IsActive = ec.IsActive,
                             CreationDate = ec.CreationDate
                         };
            return result.ToList();
        }

        // Enterprise async method (100.000+ users ready)
        public async Task<List<ExerciseCategoryDto>> GetAllCategoriesAsync()
        {
            var result = from ec in _context.ExerciseCategories
                         select new ExerciseCategoryDto
                         {
                             ExerciseCategoryID = ec.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             Description = ec.Description,
                             IsActive = ec.IsActive,
                             CreationDate = ec.CreationDate
                         };
            return await result.ToListAsync();
        }

        // Enterprise method (memory leak fixed)
        public List<ExerciseCategoryDto> GetActiveCategories()
        {
            var result = from ec in _context.ExerciseCategories
                         where ec.IsActive == true
                         orderby ec.CategoryName
                         select new ExerciseCategoryDto
                         {
                             ExerciseCategoryID = ec.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             Description = ec.Description,
                             IsActive = ec.IsActive,
                             CreationDate = ec.CreationDate
                         };
            return result.ToList();
        }

        // Enterprise async method (100.000+ users ready)
        public async Task<List<ExerciseCategoryDto>> GetActiveCategoriesAsync()
        {
            var result = from ec in _context.ExerciseCategories
                         where ec.IsActive == true
                         orderby ec.CategoryName
                         select new ExerciseCategoryDto
                         {
                             ExerciseCategoryID = ec.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             Description = ec.Description,
                             IsActive = ec.IsActive,
                             CreationDate = ec.CreationDate
                         };
            return await result.ToListAsync();
        }
    }
}
