﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserOperationClaimDal : EfEntityRepositoryBase<UserOperationClaim, GymContext>, IUserOperationClaimDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfUserOperationClaimDal(GymContext context) : base(context)
        {
        }

        // Enterprise method (memory leak fixed)
        public List<UserOperationClaimDto> GetUserOperationClaimDetails()
        {
            var result = from uoc in _context.UserOperationClaims
                         join u in _context.Users on uoc.UserId equals u.UserID
                         join oc in _context.OperationClaims on uoc.OperationClaimId equals oc.OperationClaimId
                         select new UserOperationClaimDto
                         {
                             UserOperationClaimId = uoc.UserOperationClaimId,
                             UserId = uoc.UserId,
                             UserName = u.FirstName + " " + u.LastName,
                             OperationClaimId = uoc.OperationClaimId,
                             OperationClaimName = oc.Name,
                             Email=u.Email
                         };
            return result.ToList();
        }

        // Enterprise async method (100.000+ users ready)
        public async Task<List<UserOperationClaimDto>> GetUserOperationClaimDetailsAsync()
        {
            var result = from uoc in _context.UserOperationClaims
                         join u in _context.Users on uoc.UserId equals u.UserID
                         join oc in _context.OperationClaims on uoc.OperationClaimId equals oc.OperationClaimId
                         select new UserOperationClaimDto
                         {
                             UserOperationClaimId = uoc.UserOperationClaimId,
                             UserId = uoc.UserId,
                             UserName = u.FirstName + " " + u.LastName,
                             OperationClaimId = uoc.OperationClaimId,
                             OperationClaimName = oc.Name,
                             Email=u.Email
                         };
            return await result.ToListAsync();
        }
    }
}
