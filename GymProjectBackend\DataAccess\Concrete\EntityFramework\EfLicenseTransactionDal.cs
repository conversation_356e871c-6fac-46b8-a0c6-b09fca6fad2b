﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfLicenseTransactionDal : EfEntityRepositoryBase<LicenseTransaction, GymContext>, ILicenseTransactionDal
    {
        // Enterprise constructor injection pattern (100.000+ users ready)
        public EfLicenseTransactionDal(GymContext context) : base(context)
        {
        }
    }
}
