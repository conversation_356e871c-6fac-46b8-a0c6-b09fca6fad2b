using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramTemplateDal : EfCompanyEntityRepositoryBase<WorkoutProgramTemplate, GymContext>, IWorkoutProgramTemplateDal
    {
        // Enterprise multi-tenant constructor injection pattern (100.000+ users ready)
        public EfWorkoutProgramTemplateDal(GymContext context, ICompanyContext companyContext) : base(context, companyContext)
        {
        }

        // Enterprise multi-tenant method (memory leak fixed)
        public List<WorkoutProgramTemplateListDto> GetWorkoutProgramTemplateList()
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var result = from wpt in _context.WorkoutProgramTemplates
                         where wpt.CompanyID == companyId && wpt.IsActive == true
                         select new WorkoutProgramTemplateListDto
                         {
                             WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                             ProgramName = wpt.ProgramName,
                             Description = wpt.Description,
                             ExperienceLevel = wpt.ExperienceLevel,
                             TargetGoal = wpt.TargetGoal,
                             IsActive = wpt.IsActive,
                             CreationDate = wpt.CreationDate,
                             DayCount = _context.WorkoutProgramDays
                                 .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                 .Count(),
                             ExerciseCount = _context.WorkoutProgramExercises
                                 .Where(e => _context.WorkoutProgramDays
                                     .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                     .Select(d => d.WorkoutProgramDayID)
                                     .Contains(e.WorkoutProgramDayID))
                                 .Count()
                         };

            return result.OrderByDescending(x => x.CreationDate).ToList();
        }

        // Enterprise async multi-tenant method (100.000+ users ready)
        public async Task<List<WorkoutProgramTemplateListDto>> GetWorkoutProgramTemplateListAsync()
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var result = from wpt in _context.WorkoutProgramTemplates
                         where wpt.CompanyID == companyId && wpt.IsActive == true
                         select new WorkoutProgramTemplateListDto
                         {
                             WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                             ProgramName = wpt.ProgramName,
                             Description = wpt.Description,
                             ExperienceLevel = wpt.ExperienceLevel,
                             TargetGoal = wpt.TargetGoal,
                             IsActive = wpt.IsActive,
                             CreationDate = wpt.CreationDate,
                             DayCount = _context.WorkoutProgramDays
                                 .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                 .Count(),
                             ExerciseCount = _context.WorkoutProgramExercises
                                 .Where(e => _context.WorkoutProgramDays
                                     .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                     .Select(d => d.WorkoutProgramDayID)
                                     .Contains(e.WorkoutProgramDayID))
                                 .Count()
                         };

            return await result.OrderByDescending(x => x.CreationDate).ToListAsync();
        }

        // Enterprise detail method (memory leak fixed)
        public WorkoutProgramTemplateDto GetWorkoutProgramTemplateDetail(int templateId)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var template = _context.WorkoutProgramTemplates
                .Where(wpt => wpt.WorkoutProgramTemplateID == templateId && wpt.CompanyID == companyId)
                .Select(wpt => new WorkoutProgramTemplateDto
                {
                    WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                    CompanyID = wpt.CompanyID,
                    ProgramName = wpt.ProgramName,
                    Description = wpt.Description,
                    ExperienceLevel = wpt.ExperienceLevel,
                    TargetGoal = wpt.TargetGoal,
                    IsActive = wpt.IsActive,
                    CreationDate = wpt.CreationDate,
                    DayCount = _context.WorkoutProgramDays
                        .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                        .Count()
                })
                .FirstOrDefault();

            if (template != null)
            {
                // Enterprise data loading
                template.Days = GetWorkoutProgramDays(templateId);
            }

            return template;
        }

        // Enterprise async detail method (100.000+ users ready)
        public async Task<WorkoutProgramTemplateDto> GetWorkoutProgramTemplateDetailAsync(int templateId)
        {
            // Enterprise multi-tenant security
            int companyId = _companyContext.GetCompanyId();

            var template = await _context.WorkoutProgramTemplates
                .Where(wpt => wpt.WorkoutProgramTemplateID == templateId && wpt.CompanyID == companyId)
                .Select(wpt => new WorkoutProgramTemplateDto
                {
                    WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                    CompanyID = wpt.CompanyID,
                    ProgramName = wpt.ProgramName,
                    Description = wpt.Description,
                    ExperienceLevel = wpt.ExperienceLevel,
                    TargetGoal = wpt.TargetGoal,
                    IsActive = wpt.IsActive,
                    CreationDate = wpt.CreationDate,
                    DayCount = _context.WorkoutProgramDays
                        .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                        .Count()
                })
                .FirstOrDefaultAsync();

            if (template != null)
            {
                // Enterprise async data loading
                template.Days = await GetWorkoutProgramDaysAsync(templateId);
            }

            return template;
        }

        // Enterprise helper method (memory leak fixed)
        private List<WorkoutProgramDayDto> GetWorkoutProgramDays(int templateId)
        {
            var days = _context.WorkoutProgramDays
                .Where(d => d.WorkoutProgramTemplateID == templateId)
                .Select(d => new WorkoutProgramDayDto
                {
                    WorkoutProgramDayID = d.WorkoutProgramDayID,
                    WorkoutProgramTemplateID = d.WorkoutProgramTemplateID,
                    DayNumber = d.DayNumber,
                    DayName = d.DayName,
                    IsRestDay = d.IsRestDay,
                    CreationDate = d.CreationDate
                })
                .OrderBy(d => d.DayNumber)
                .ToList();

            // Enterprise data loading for each day
            foreach (var day in days)
            {
                day.Exercises = GetWorkoutProgramExercises(day.WorkoutProgramDayID);
            }

            return days;
        }

        // Enterprise async helper method (100.000+ users ready)
        private async Task<List<WorkoutProgramDayDto>> GetWorkoutProgramDaysAsync(int templateId)
        {
            var days = await _context.WorkoutProgramDays
                .Where(d => d.WorkoutProgramTemplateID == templateId)
                .Select(d => new WorkoutProgramDayDto
                {
                    WorkoutProgramDayID = d.WorkoutProgramDayID,
                    WorkoutProgramTemplateID = d.WorkoutProgramTemplateID,
                    DayNumber = d.DayNumber,
                    DayName = d.DayName,
                    IsRestDay = d.IsRestDay,
                    CreationDate = d.CreationDate
                })
                .OrderBy(d => d.DayNumber)
                .ToListAsync();

            // Enterprise async data loading for each day
            foreach (var day in days)
            {
                day.Exercises = await GetWorkoutProgramExercisesAsync(day.WorkoutProgramDayID);
            }

            return days;
        }

        // Enterprise exercise helper method (memory leak fixed)
        private List<WorkoutProgramExerciseDto> GetWorkoutProgramExercises(int dayId)
        {
            var exercises = from wpe in _context.WorkoutProgramExercises
                           where wpe.WorkoutProgramDayID == dayId
                           select new WorkoutProgramExerciseDto
                           {
                               WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                               WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                               ExerciseType = wpe.ExerciseType,
                               ExerciseID = wpe.ExerciseID,
                               OrderIndex = wpe.OrderIndex,
                               Sets = wpe.Sets,
                               Reps = wpe.Reps,
                               RestTime = wpe.RestTime,
                               Notes = wpe.Notes,
                               CreationDate = wpe.CreationDate
                           };

            var result = exercises.OrderBy(e => e.OrderIndex).ToList();

            // Enterprise exercise name loading
            foreach (var exercise in result)
            {
                if (exercise.ExerciseType == "System")
                {
                    var systemExercise = _context.SystemExercises
                        .Where(se => se.SystemExerciseID == exercise.ExerciseID)
                        .Select(se => new { se.ExerciseName, se.Description, CategoryName = _context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == se.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                        .FirstOrDefault();

                    if (systemExercise != null)
                    {
                        exercise.ExerciseName = systemExercise.ExerciseName;
                        exercise.ExerciseDescription = systemExercise.Description;
                        exercise.CategoryName = systemExercise.CategoryName;
                    }
                }
                else if (exercise.ExerciseType == "Company")
                {
                    // Enterprise multi-tenant security
                    int companyId = _companyContext.GetCompanyId();
                    var companyExercise = _context.CompanyExercises
                        .Where(ce => ce.CompanyExerciseID == exercise.ExerciseID && ce.CompanyID == companyId)
                        .Select(ce => new { ce.ExerciseName, ce.Description, CategoryName = _context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == ce.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                        .FirstOrDefault();

                    if (companyExercise != null)
                    {
                        exercise.ExerciseName = companyExercise.ExerciseName;
                        exercise.ExerciseDescription = companyExercise.Description;
                        exercise.CategoryName = companyExercise.CategoryName;
                    }
                }
            }

            return result;
        }

        // Enterprise async exercise helper method (100.000+ users ready)
        private async Task<List<WorkoutProgramExerciseDto>> GetWorkoutProgramExercisesAsync(int dayId)
        {
            var exercises = from wpe in _context.WorkoutProgramExercises
                           where wpe.WorkoutProgramDayID == dayId
                           select new WorkoutProgramExerciseDto
                           {
                               WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                               WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                               ExerciseType = wpe.ExerciseType,
                               ExerciseID = wpe.ExerciseID,
                               OrderIndex = wpe.OrderIndex,
                               Sets = wpe.Sets,
                               Reps = wpe.Reps,
                               RestTime = wpe.RestTime,
                               Notes = wpe.Notes,
                               CreationDate = wpe.CreationDate
                           };

            var result = await exercises.OrderBy(e => e.OrderIndex).ToListAsync();

            // Enterprise async exercise name loading
            foreach (var exercise in result)
                {
                    if (exercise.ExerciseType == "System")
                    {
                        var systemExercise = await _context.SystemExercises
                            .Where(se => se.SystemExerciseID == exercise.ExerciseID)
                            .Select(se => new { se.ExerciseName, se.Description, CategoryName = _context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == se.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                            .FirstOrDefaultAsync();

                        if (systemExercise != null)
                        {
                            exercise.ExerciseName = systemExercise.ExerciseName;
                            exercise.ExerciseDescription = systemExercise.Description;
                            exercise.CategoryName = systemExercise.CategoryName;
                        }
                    }
                    else if (exercise.ExerciseType == "Company")
                    {
                        // Enterprise multi-tenant security
                        int companyId = _companyContext.GetCompanyId();
                        var companyExercise = await _context.CompanyExercises
                            .Where(ce => ce.CompanyExerciseID == exercise.ExerciseID && ce.CompanyID == companyId)
                            .Select(ce => new { ce.ExerciseName, ce.Description, CategoryName = _context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == ce.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                            .FirstOrDefaultAsync();

                        if (companyExercise != null)
                        {
                            exercise.ExerciseName = companyExercise.ExerciseName;
                            exercise.ExerciseDescription = companyExercise.Description;
                            exercise.CategoryName = companyExercise.CategoryName;
                        }
                    }
                }

                return result;
            }

            return result;
        }
    }
}
